"""应用依赖项

提供用于FastAPI依赖注入的函数，用于获取应用状态中的服务实例。
"""

from fastapi import Request
from utils.task_manager import TaskManager
from services.build_service import BuildService
from services.test_service import TestService
from services.credential_service import CredentialService

def get_task_manager(request: Request) -> TaskManager:
    """从应用状态获取任务管理器实例"""
    return request.app.state.task_manager

def get_build_service(request: Request) -> BuildService:
    """从应用状态获取构建服务实例"""
    return request.app.state.build_service

def get_test_service(request: Request) -> TestService:
    """从应用状态获取测试服务实例"""
    return request.app.state.test_service

def get_credential_service(request: Request) -> CredentialService:
    """从应用状态获取凭据服务实例"""
    return request.app.state.credential_service
