"""任务管理器

提供任务的创建、存储、查询和管理功能。
"""

import uuid
from datetime import datetime
from typing import Optional, Any
from models.task_models import TaskStatus, BuildTask, TestTask
from models.response_models import TaskSummary
from core.logging import get_logger
from core.exceptions import TaskNotFoundException
from config.redis_config import redis_config
from .redis_client import RedisClient

logger = get_logger(__name__)


class TaskManager:
    """任务管理器"""
    
    def __init__(self, redis_client: RedisClient):
        self.redis_client = redis_client
    
    def generate_task_id(self) -> str:
        """生成任务ID"""
        return str(uuid.uuid4())
    
    async def create_build_task(
        self,
        remote_ip: str,
        ssh_user: str,
        container_name: str,
        message: str,
        **kwargs
    ) -> BuildTask:
        """创建构建任务
        
        Args:
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            container_name: 容器名称
            message: 任务消息
            **kwargs: 其他参数
            
        Returns:
            构建任务对象
        """
        task_id = self.generate_task_id()
        task = BuildTask(
            task_id=task_id,
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            container_name=container_name,
            message=message,
            **kwargs
        )
        
        # 存储到Redis
        await self.save_build_task(task)
        logger.info(f"创建构建任务: {task_id}")
        return task
    
    async def create_test_task(
        self,
        remote_ip: str,
        ssh_user: str,
        wait_seconds: int,
        log_lines: int,
        message: str,
        pcap_file_path: Optional[str] = None,
        filename: Optional[str] = None,
        pcap_file_name: Optional[str] = None,
        eth_name: Optional[str] = None,
        protocol_name: Optional[str] = None
    ) -> TestTask:
        """创建测试任务

        Args:
            remote_ip: 远程服务器IP
            ssh_user: SSH用户名
            wait_seconds: 等待时间
            log_lines: 日志行数
            message: 任务消息
            pcap_file_path: pcap文件路径
            filename: 文件名
            pcap_file_name: pcap文件名（不包含路径）
            eth_name: 网口名称
            protocol_name: 测试协议名称

        Returns:
            测试任务对象
        """
        task_id = self.generate_task_id()
        task = TestTask(
            task_id=task_id,
            pcap_file_path=pcap_file_path,
            filename=filename,
            pcap_file_name=pcap_file_name,
            protocol_name=protocol_name,
            remote_ip=remote_ip,
            ssh_user=ssh_user,
            wait_seconds=wait_seconds,
            log_lines=log_lines,
            message=message,
            eth_name=eth_name
        )
        
        # 存储到Redis
        await self.save_test_task(task)
        logger.info(f"创建测试任务: {task_id}")
        return task
    
    async def save_build_task(self, task: BuildTask) -> bool:
        """保存构建任务
        
        Args:
            task: 构建任务对象
            
        Returns:
            是否成功
        """
        key = redis_config.get_build_task_key(task.task_id)
        task_data = task.dict()
        
        # 存储任务数据
        success = await self.redis_client.set_task(
            key, task_data, redis_config.task_expire_time
        )
        
        if success:
            # 添加到任务集合
            await self.redis_client.add_to_set(
                redis_config.build_tasks_set, task.task_id
            )
        
        return success
    
    async def save_test_task(self, task: TestTask) -> bool:
        """保存测试任务
        
        Args:
            task: 测试任务对象
            
        Returns:
            是否成功
        """
        key = redis_config.get_test_task_key(task.task_id)
        task_data = task.dict()
        
        # 存储任务数据
        success = await self.redis_client.set_task(
            key, task_data, redis_config.task_expire_time
        )
        
        if success:
            # 添加到任务集合
            await self.redis_client.add_to_set(
                redis_config.test_tasks_set, task.task_id
            )
        
        return success
    
    async def get_build_task(self, task_id: str) -> BuildTask:
        """获取构建任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            构建任务对象
            
        Raises:
            TaskNotFoundException: 任务不存在
        """
        key = redis_config.get_build_task_key(task_id)
        task_data = await self.redis_client.get_task(key)
        
        if not task_data:
            raise TaskNotFoundException(task_id)
        
        return BuildTask(**task_data)
    
    async def get_test_task(self, task_id: str) -> TestTask:
        """获取测试任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            测试任务对象
            
        Raises:
            TaskNotFoundException: 任务不存在
        """
        key = redis_config.get_test_task_key(task_id)
        task_data = await self.redis_client.get_task(key)
        
        if not task_data:
            raise TaskNotFoundException(task_id)
        
        return TestTask(**task_data)
    
    async def update_build_task(self, task: BuildTask) -> bool:
        """更新构建任务
        
        Args:
            task: 构建任务对象
            
        Returns:
            是否成功
        """
        return await self.save_build_task(task)
    
    async def update_test_task(self, task: TestTask) -> bool:
        """更新测试任务
        
        Args:
            task: 测试任务对象
            
        Returns:
            是否成功
        """
        return await self.save_test_task(task)
    
    async def update_task_status(
        self, 
        task_id: str, 
        status: TaskStatus, 
        message: str, 
        result: Optional[dict[str, Any]] = None
    ) -> bool:
        """更新任务状态（通用方法）
        
        Args:
            task_id: 任务ID
            status: 新状态
            message: 状态消息
            result: 任务结果（可选）
            
        Returns:
            是否成功
        """
        try:
            # 先尝试作为构建任务更新
            try:
                task = await self.get_build_task(task_id)
                task.status = status
                task.message = message
                if status == TaskStatus.RUNNING and not task.started_at:
                    task.started_at = datetime.now()
                elif status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.ERROR]:
                    task.completed_at = datetime.now()
                    if result:
                        task.stdout = result.get('stdout', '')
                        task.stderr = result.get('stderr', '')
                        task.return_code = result.get('return_code', 0)
                return await self.update_build_task(task)
            except TaskNotFoundException:
                pass
            
            # 再尝试作为测试任务更新
            try:
                task = await self.get_test_task(task_id)
                task.status = status
                task.message = message
                if status == TaskStatus.RUNNING and not task.started_at:
                    task.started_at = datetime.now()
                elif status in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.ERROR]:
                    task.completed_at = datetime.now()
                    if result:
                        # 对于测试任务，将结果存储在相应字段中
                        logger.info(f"更新测试任务 {task_id} 结果: {result.keys() if result else 'None'}")
                        if 'logs' in result:
                            logs = result['logs']
                            logger.info(f"日志数据: hw_log长度={len(logs.get('hw_log', ''))}, "
                                       f"hw_err长度={len(logs.get('hw_err', ''))}, "
                                       f"json_events数量={len(logs.get('json_events', []))}")
                            task.hw_log = logs.get('hw_log', '')
                            task.hw_err = logs.get('hw_err', '')
                            if 'json_events' in logs:
                                task.json_events = logs['json_events']
                        else:
                            logger.warning(f"测试任务 {task_id} 结果中没有找到 'logs' 字段")
                return await self.update_test_task(task)
            except TaskNotFoundException:
                pass
            
            logger.error(f"任务不存在，无法更新状态: {task_id}")
            return False
            
        except Exception as e:
            logger.error(f"更新任务状态失败: {task_id}, 错误: {e}")
            return False
    
    async def delete_build_task(self, task_id: str) -> bool:
        """删除构建任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        # 检查任务是否存在且已完成
        try:
            task = await self.get_build_task(task_id)
            if task.status not in [TaskStatus.SUCCESS, TaskStatus.FAILED, TaskStatus.ERROR]:
                return False
        except TaskNotFoundException:
            return False
        
        # 删除任务数据
        key = redis_config.get_build_task_key(task_id)
        success = await self.redis_client.delete_task(key)
        
        if success:
            # 从任务集合中移除
            await self.redis_client.remove_from_set(
                redis_config.build_tasks_set, task_id
            )
        
        return success
    
    async def delete_test_task(self, task_id: str) -> bool:
        """删除测试任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        key = redis_config.get_test_task_key(task_id)
        success = await self.redis_client.delete_task(key)
        
        if success:
            # 从任务集合中移除
            await self.redis_client.remove_from_set(
                redis_config.test_tasks_set, task_id
            )
        
        return success
    
    async def get_task(self, task_id: str):
        """获取任务（通用方法）
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务对象或None
        """
        # 先尝试获取构建任务
        try:
            return await self.get_build_task(task_id)
        except TaskNotFoundException:
            pass
        
        # 再尝试获取测试任务
        try:
            return await self.get_test_task(task_id)
        except TaskNotFoundException:
            pass
        
        return None
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务（通用方法）
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功
        """
        # 先尝试删除构建任务
        if await self.delete_build_task(task_id):
            return True
        
        # 再尝试删除测试任务
        if await self.delete_test_task(task_id):
            return True
        
        return False
    
    async def delete_task_delayed(self, task_id: str, delay: int = 1) -> None:
        """延迟删除任务
        
        Args:
            task_id: 任务ID
            delay: 延迟时间（秒）
        """
        import asyncio
        await asyncio.sleep(delay)
        try:
            success = await self.delete_task(task_id)
            if success:
                logger.info(f"延迟删除任务成功: {task_id}")
            else:
                logger.warning(f"延迟删除任务失败，任务可能不存在: {task_id}")
        except Exception as e:
            logger.error(f"延迟删除任务异常: {task_id}, 错误: {e}")
    
    async def get_tasks(self, status: Optional[str] = None, limit: int = 50):
        """获取任务列表（通用方法）
        
        Args:
            status: 状态过滤
            limit: 限制数量
            
        Returns:
            任务列表
        """
        from models.task_models import TaskStatus
        
        status_filter = None
        if status:
            try:
                status_filter = TaskStatus(status)
            except ValueError:
                pass
        
        # 获取构建任务
        build_tasks = await self.list_build_tasks(limit=limit//2, status_filter=status_filter)
        
        # 获取测试任务
        test_tasks = await self.list_test_tasks(limit=limit//2, status_filter=status_filter)
        
        # 合并并按创建时间排序
        all_tasks = build_tasks + test_tasks
        all_tasks.sort(key=lambda x: x.created_at, reverse=True)
        
        return all_tasks[:limit]
    
    async def list_build_tasks(
        self, 
        limit: int = 10, 
        status_filter: Optional[TaskStatus] = None
    ) -> list[TaskSummary]:
        """列出构建任务
        
        Args:
            limit: 限制数量
            status_filter: 状态过滤
            
        Returns:
            任务摘要列表
        """
        task_ids = await self.redis_client.get_set_members(
            redis_config.build_tasks_set, limit * 2  # 获取更多以便过滤
        )
        
        tasks = []
        for task_id in task_ids:
            try:
                task = await self.get_build_task(task_id)
                if status_filter is None or task.status == status_filter:
                    tasks.append(TaskSummary(
                        task_id=task.task_id,
                        status=task.status,
                        message=task.message,
                        created_at=task.created_at,
                        completed_at=task.completed_at
                    ))
                    
                    if len(tasks) >= limit:
                        break
                        
            except TaskNotFoundException:
                # 任务不存在，从集合中移除
                await self.redis_client.remove_from_set(
                    redis_config.build_tasks_set, task_id
                )
        
        # 按创建时间倒序排列
        tasks.sort(key=lambda x: x.created_at, reverse=True)
        return tasks
    
    async def list_test_tasks(
        self, 
        limit: int = 10, 
        status_filter: Optional[TaskStatus] = None
    ) -> list[TaskSummary]:
        """列出测试任务
        
        Args:
            limit: 限制数量
            status_filter: 状态过滤
            
        Returns:
            任务摘要列表
        """
        task_ids = await self.redis_client.get_set_members(
            redis_config.test_tasks_set, limit * 2  # 获取更多以便过滤
        )
        
        tasks = []
        for task_id in task_ids:
            try:
                task = await self.get_test_task(task_id)
                if status_filter is None or task.status == status_filter:
                    tasks.append(TaskSummary(
                        task_id=task.task_id,
                        status=task.status,
                        message=task.message,
                        filename=task.filename,
                        created_at=task.created_at,
                        completed_at=task.completed_at
                    ))
                    
                    if len(tasks) >= limit:
                        break
                        
            except TaskNotFoundException:
                # 任务不存在，从集合中移除
                await self.redis_client.remove_from_set(
                    redis_config.test_tasks_set, task_id
                )
        
        # 按创建时间倒序排列
        tasks.sort(key=lambda x: x.created_at, reverse=True)
        return tasks

    async def cleanup_completed_tasks(self) -> int:
        """清理已完成的任务
        
        Returns:
            清理的任务数量
        """
        cleaned_count = 0
        
        # 清理构建任务
        build_task_ids = await self.redis_client.get_set_members(
            redis_config.build_tasks_set
        )
        
        for task_id in build_task_ids:
            try:
                task = await self.get_build_task(task_id)
                if task.status in ["success", "failed", "error"]:
                    if await self.delete_build_task(task_id):
                        cleaned_count += 1
                        logger.info(f"清理已完成的构建任务: {task_id}")
            except TaskNotFoundException:
                # 任务不存在，从集合中移除
                await self.redis_client.remove_from_set(
                    redis_config.build_tasks_set, task_id
                )
        
        # 清理测试任务
        test_task_ids = await self.redis_client.get_set_members(
            redis_config.test_tasks_set
        )
        
        for task_id in test_task_ids:
            try:
                task = await self.get_test_task(task_id)
                if task.status in ["success", "failed", "error"]:
                    if await self.delete_test_task(task_id):
                        cleaned_count += 1
                        logger.info(f"清理已完成的测试任务: {task_id}")
            except TaskNotFoundException:
                # 任务不存在，从集合中移除
                await self.redis_client.remove_from_set(
                    redis_config.test_tasks_set, task_id
                )
        
        if cleaned_count > 0:
            logger.info(f"批量清理完成，共清理 {cleaned_count} 个已完成的任务")
        
        return cleaned_count 